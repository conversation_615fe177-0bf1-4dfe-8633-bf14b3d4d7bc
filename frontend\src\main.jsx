
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import { Provider } from 'react-redux'
import store from './redux/store.js'
import { BrowserRouter } from 'react-router-dom'
import { ServerProvider } from './context/ServerProvider.jsx'
import ThemeProvider from './context/ThemeProvider.jsx'


createRoot(document.getElementById('root')).render(

  

  <BrowserRouter>
    <Provider store={store}>
      <ServerProvider>
        <ThemeProvider>
          <App />
        </ThemeProvider>
      </ServerProvider>
    </Provider>
  </BrowserRouter>

)
