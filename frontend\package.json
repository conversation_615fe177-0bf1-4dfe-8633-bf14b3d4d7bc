{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@greatsumini/react-facebook-login": "^3.4.0", "@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.10", "axios": "^1.9.0", "dotenv": "^16.5.0", "emoji-picker-react": "^4.12.3", "firebase": "^11.9.1", "framer-motion": "^12.23.3", "gsap": "^3.13.0", "lightningcss": "^1.30.1", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "motion": "^12.23.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "socket.io-client": "^4.8.1", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.10", "timeago.js": "^4.0.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}