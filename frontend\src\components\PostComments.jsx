import { useContext, useEffect, useState } from 'react';
import { format, register } from 'timeago.js';
import heartfill from '../assets/heartfill.png'
import { GoHeart } from "react-icons/go";
import { useSelector } from 'react-redux';
import axios from 'axios';
import { ThemeContext } from '../context/ContextProvider';
import { RxDotsHorizontal } from "react-icons/rx";
import dp from '../assets/dp.webp'
import React from 'react';
import { useNavigate } from 'react-router-dom';


const PostComments = ({ posts, indexval, allComments, allReplies, showReplies, setShowReplies, setReply, setCommentId, setComment, status }) => {

    const { userData } = useSelector((state) => state.user)
    const [more, setMore] = useState(false)
    const navigate = useNavigate()
    const [showOptions, setShowOptions] = useState('')
    const [deleteComment, setDeleteComment] = useState(false)
    const [deleteReply, setDeleteReply] = useState(false)
    const [loading, setLoading] = useState(false)
    const serverUrl = import.meta.env.VITE_SERVER_URL || "http://localhost:8000";
    const { theme } = useContext(ThemeContext)
    let mainTextTheme = theme === 'dark' ? 'text-white' : (theme === 'light') ? 'text-black' : ' text-black dark:text-white'
    let subTextTheme = theme === 'dark' ? 'text-[#ffffffa5]' : (theme === 'light') ? 'text-[#000000a5]' : ' text-[#000000a5] dark:text-[#ffffffa5]'
    let hoverTheme = theme === 'dark' ? 'hover:text-[#ffffff81]' : (theme === 'light') ? 'hover:text-[#00000081]' : 'hover:text-[#00000081] dark:hover:text-[#ffffff81]'

    useEffect(() => {
        if (posts && posts[indexval]?.description && posts[indexval]?.description.length > 30) {
            setMore(true)
        } else {
            setMore(false)
        }
    }, [posts, indexval])


    register('short', (number, index) => {
        return [
            ['just now', 'right now'],
            ['1m', 'in 1m'],
            ['%sm', 'in %sm'],
            ['1hr', 'in 1hr'],
            ['%shr', 'in %shr'],
            ['1d', 'in 1d'],
            ['%sd', 'in %sd'],
            ['1w', 'in 1w'],
            ['%sw', 'in %sw'],
            ['1mo', 'in 1mo'],
            ['%smo', 'in %smo'],
            ['1yr', 'in 1yr'],
            ['%syr', 'in %syr'],
        ][index];
    });



    const handleCommentLike = async (comment) => {
        try {
            let result = await axios.get(`${serverUrl}/api/posts/likecomment/${comment._id}`, { withCredentials: true });
            console.log(result.data);
        } catch (error) {
            console.log(error);
        }

    }

    const handleReplyLike = async (reply) => {
        try {
            let result = await axios.get(`${serverUrl}/api/posts/likereply/${reply._id}`, { withCredentials: true });
            console.log(result.data);
        } catch (error) {
            console.log(error);
        }

    }

    const handleDeleteComment = async (commentId) => {
        setLoading(true)
        try {
            let result = await axios.delete(`${serverUrl}/api/posts/deletecomment/${commentId}`, { withCredentials: true });
            console.log(result.data);
            setDeleteComment(false)
            setLoading(false)
        } catch (error) {
            console.log(error);
            setLoading(false)
        }

    }


    const handleDeleteReply = async (replyId) => {
        setLoading(true)
        try {
            let result = await axios.delete(`${serverUrl}/api/posts/deletereply/${replyId}`, { withCredentials: true });
            console.log(result.data);
            setDeleteReply(false)
            setLoading(false)
        } catch (error) {
            console.log(error);
            setLoading(false)
        }

    }

    const handleNavigate = (username, authorId) => {
        navigate(`/profile/${username || authorId}`)
        window.scrollTo(0, 0)
        
    }

  


    if (status === 'loading') return <div className=" w-full h-80 md:h-110 flex items-center justify-center">
        <div className="h-15 w-15 border-t-1 border-b-1  border-[white] rounded-full animate-spin transition-all duration-500 ease-in-out" />
    </div>

    return (
        <>
            
            {(posts && posts[indexval]?.comments?.length == 0 && !posts[indexval]?.description) ?
                <div className="w-full h-fit md:h-110  hidden md:flex flex-col items-center justify-center">
                    <p className={`${mainTextTheme} text-2xl font-medium ml-2 pb-1`}>No comments yet.</p>
                    <p className={`${subTextTheme} text-sm font-medium ml-2`}>Start the conversation</p>
                </div> :
                <>
                    <div className={`w-full flex flex-col items-start justify-start ${allComments && allComments.length > 0 ? 'h-fit ' : 'h-150'}`}>
                        
                        {posts && posts[indexval]?.description && <div className="w-full cursor-pointer h-fit flex items-start justify-start p-4 gap-2 ">
                            
                            <img onClick={() => { handleNavigate(posts[indexval]?.author?.username, posts[indexval]?.author?._id) }} src={posts[indexval]?.author?.profilepic} alt="author image" className="w-9 h-9 z-0 rounded-full object-cover cursor-pointer" />
                            <div className="w-full h-fit flex flex-col items-start justify-start">
                                <div className=" w-fit h-fit flex items-center justify-start gap-2">
                                    <p onClick={() => { handleNavigate(posts[indexval]?.author?.username, posts[indexval]?.author?._id) }} className={`${mainTextTheme} text-sm font-medium ml-2`}>{posts[indexval]?.author?.username || posts[indexval]?.author?.name}</p>
                                    <p className={`${subTextTheme} ${theme === 'dark' ? 'bg-[#78787884]' : (theme === 'light') ? 'bg-[#81818184]' : ' bg-[#81818184] dark:bg-[#78787884]'} px-1 rounded-2xl text-xs font-medium ml-2`}>Author</p>
                                </div>
                                <p className={` text-sm pl-2 break-words whitespace-normal ${mainTextTheme} `}>{more ? posts[indexval]?.description : posts[indexval]?.description?.slice(0, 30)} <span onClick={() => { setMore(!more) }} className="text-[#ffffff84] text-sm cursor-pointer hover:text-[#ffffff81] transition-all duration-200 ease-in-out">{more ? '...more' : ''}</span> </p>
                                <div className="w-fit h-fit flex items-center justify-start gap-2">
                                    <p className={`text-xs ml-2 ${subTextTheme}`}>{format(posts[indexval].createdAt, 'short')}</p>
                                    <p onClick={() => { setComment(`@${posts[indexval].author?.username} `); setReply(true); setCommentId(posts[indexval]._id) }} className={`text-xs ml-2 cursor-pointer ${theme === 'dark' ? 'hover:text-[#ffffff81]' : (theme === 'light') ? 'hover:text-[#00000081]' : 'hover:text-[#00000081] dark:hover:text-[#ffffff81]'} transition-all duration-200 ease-in-out ${subTextTheme}`}>reply</p>
                                   
                                </div>
                            </div>
                            <div className="w-fit h-full flex flex-col items-center justify-center ">
                                {(allComments && allComments?.likes?.some(user => user._id === userData?.user?._id)) ? <img onClick={() => { handleCommentLike() }} src={heartfill} alt="liked" className="w-3 h-3" /> : <GoHeart size={20} onClick={() => { handleCommentLike() }} className={` cursor-pointer transition-all duration-200 ease-in-out ${hoverTheme} ${mainTextTheme} `} />}
                                {allComments && <p className={`text-xs ml-2${mainTextTheme}`}>{allComments?.likes?.length}</p>}
                            </div>
                        </div>}
                    </div>
                    {
                        allComments && allComments.map((comment) => (
                            <div key={comment._id} className={`  w-full md:h-fit flex flex-col items-start justify-start px-4`}>

                                <div className={`w-full h-fit flex items-start justify-between py-4 cursor-pointer gap-2`}>
                                    <img onClick={() => { handleNavigate(comment?.author?.username, comment?.author?._id) }} src={comment?.author?.profilepic} alt="author image" className="w-9 h-9 z-0 rounded-full object-cover" />
                                    <div onMouseOver={() => { setShowOptions(`${comment._id}`) }} onMouseLeave={() => { setShowOptions('') }} className="w-full h-fit flex flex-col items-start justify-start ">
                                        <p onClick={() => { handleNavigate(comment?.author?.username, comment?.author?._id) }} className={`${mainTextTheme} text-sm font-medium ml-2`}>{comment.author?.username || comment?.author?.name}</p>
                                        <div className="w-full 2xl:w-90 pr-2 h-fit overflow-hidden">
                                            <p className={`${mainTextTheme}  text-sm pl-2 break-words whitespace-normal `}>{comment.comment}</p>
                                        </div>
                                        <div className="w-fit h-fit flex items-center justify-start gap-2">
                                            <p className={` text-xs ml-2 ${subTextTheme} `}>{format(comment.createdAt, 'short')}</p>
                                            <p onClick={() => { setComment(`@${comment.author?.username} `); setReply(true); setCommentId(comment._id) }} className={`text-xs ml-2 cursor-pointer ${hoverTheme} transition-all duration-200 ease-in-out ${subTextTheme}`}>reply</p>
                                            {<RxDotsHorizontal onClick={() => { setDeleteComment(true) }} size={20} className={`${mainTextTheme} ml-3 cursor-pointer  ${(comment?.author?.username === userData?.user?.username) ? (showOptions === comment._id) ? 'opacity-100' : 'opacity-0' : 'hidden'} transition-all duration-200 ease-in-out ${hoverTheme}`} />}
                                        </div>
                                        {deleteComment && <div onClick={() => { setDeleteComment(false) }} className={`fixed top-0 left-0 z-50  w-screen h-screen flex flex-col items-center justify-center bg-[#00000080] md:bg-[#0000004a]`}>
                                            {!loading && <div className={`w-fit h-fit flex flex-col items-center justify-center py-2 gap-3  rounded-2xl ${theme === 'dark' ? 'bg-[#262626] ' : (theme === 'light') ? 'bg-white' : ' bg-white dark:bg-[#262626]'}`}>
                                                <p onClick={() => { handleDeleteComment(comment._id) }} className={` text-sm font-bold py-2 cursor-pointer px-20 border-b-1 text-[#ff0000] md:px-40 ${theme === 'dark' ? 'border-[#464545]' : (theme === 'light') ? 'border-[#b5b3b37c]' : ' border-[#818181] dark:border-[#787878]'}`}>Delete</p>
                                                <p onClick={() => { setDeleteComment(false) }} className={`${mainTextTheme} pb-2 px-20 md:px-40 text-sm font-medium  cursor-pointer ${hoverTheme} transition-all duration-200 ease-in-out`}>Cancel</p>
                                            </div>}
                                            {loading && <div className="h-10 w-10 border-t-1 border-b-1  border-[white] rounded-full animate-spin transition-all duration-500 ease-in-out" />}
                                        </div>}
                                    </div>
                                    <div className="w-fit h-full flex flex-col items-center justify-center">
                                        {(allComments && comment?.likes?.some(user => user._id === userData?.user?._id)) ? <img onClick={() => { handleCommentLike(comment) }} src={heartfill} alt="liked" className="w-5 h-5 cursor-pointer" /> : <GoHeart size={20} onClick={() => { handleCommentLike(comment) }} className={`${mainTextTheme} cursor-pointer transition-all duration-200 ease-in-out ${hoverTheme}`} />}
                                        {allComments && <p className={`${mainTextTheme} text-xs`}>{comment?.likes?.length}</p>}
                                    </div>

                                </div>
                                {(allReplies && comment.replies?.length > 0 ) && <div onClick={() => { setShowReplies(prev => ({ ...prev, [comment._id]: !prev[comment._id] })) }} className="transition-all duration-200 ease-in-out cursor-pointer w-full h-fit flex items-center pl-14 justify-start gap-2"><hr className={`w-5 h-[1px] border-none bg-[#959393] mt-1 `} /><p className={`${hoverTheme} text-[#959393]  text-xs ml-2 cursor-pointer transition-all duration-200 ease-in-out`}>{showReplies[comment._id] ? 'hide replies' : `view replies (${comment.replies?.length})`}</p></div>}
                                {allReplies && allReplies.map((reply) => (
                                    <div key={reply._id} className={`${showReplies[comment._id] ? 'h-fit ' : 'hidden'} w-full h-fit flex flex-col items-start justify-start pl-4 ${reply.comment === comment._id ? '' : 'hidden'} `}>
                                        {deleteReply && <div onClick={() => { setDeleteComment(false) }} className={`fixed top-0 left-0 z-50  w-screen h-screen flex flex-col items-center justify-center bg-[#17171759]`}>
                                            {!loading && <div className={`w-fit h-fit flex flex-col items-center justify-center py-2 gap-3  rounded-lg ${theme === 'dark' ? 'bg-[#262626]' : (theme === 'light') ? 'bg-white' : ' bg-white dark:bg-[#262626]'}`}>
                                                <p onClick={() => { handleDeleteReply(reply._id) }} className={` text-sm font-bold py-2 cursor-pointer  border-b-1 text-[#ff0000] px-40 ${theme === 'dark' ? 'border-[#787878]' : (theme === 'light') ? 'border-[#b5b3b37c]' : ' border-[#818181] dark:border-[#787878]'}`}>Delete</p>
                                                <p onClick={() => { setDeleteReply(false) }} className={`${mainTextTheme} pb-2 px-40 text-sm font-medium  cursor-pointer ${hoverTheme} transition-all duration-200 ease-in-out`}>Cancel</p>
                                            </div>}
                                            {loading && <div className="h-10 w-10 border-t-1 border-b-1  border-[white] rounded-full animate-spin transition-all duration-500 ease-in-out" />}
                                        </div>}
                                        <div className={`w-full h-fit flex items-start justify-start pr-0 p-4 gap-2`}>
                                            {<img onClick={() => { handleNavigate(reply?.author?.username, reply?.author?._id) }} src={reply.author?.profilepic || dp} alt="author image" className="w-9 h-9 z-0 rounded-full object-cover" />}
                                            <div className="w-full h-fit flex items-start justify-between">
                                                <div onMouseOver={() => { setShowOptions(`${reply._id}`) }} onMouseLeave={() => { setShowOptions('') }} className="w-full h-fit flex flex-col items-start justify-start">
                                                    <p onClick={() => { handleNavigate(reply?.author?.username, reply?.author?._id) }} className={`${mainTextTheme} text-sm font-medium ml-2`}>{reply.author?.username || reply?.author?.name}</p>
                                                    <div>
                                                        {
                                                            reply.reply.startsWith('@') ? (
                                                                <p className={`${mainTextTheme} text-sm pl-2 `}>
                                                                    <span className="text-[#0095f6] font-medium">
                                                                        {reply.reply.split(' ')[0]}
                                                                    </span>{" "}
                                                                    {reply.reply.split(' ').slice(1).join(' ')}
                                                                </p>
                                                            ) : (
                                                                <p className={`${mainTextTheme} text-sm`}>{reply?.reply}</p>
                                                            )
                                                        }
                                                    </div>
                                                    <div className="w-fit h-fit flex items-center justify-start gap-2">
                                                        <p className={`${subTextTheme} text-xs ml-2`}>{format(reply.createdAt, 'short')}</p>
                                                        <p onClick={() => { setComment(`@${reply.author?.username} `); setReply(true); setCommentId(reply.comment); }} className={`${subTextTheme} text-xs ml-2 cursor-pointer ${hoverTheme} transition-all duration-200 ease-in-out`}>reply</p>
                                                        {<RxDotsHorizontal onClick={() => { setDeleteReply(true) }} size={20} className={`${mainTextTheme} ml-3 cursor-pointer  ${(reply?.author?.username === userData?.user?.username) ? (showOptions === reply._id) ? 'opacity-100' : 'opacity-0' : 'hidden'} transition-all duration-200 ease-in-out ${hoverTheme}`} />}
                                                    </div>
                                                </div>

                                            </div>
                                            <div className="w-fit h-full flex flex-col items-center justify-center">
                                                {(reply?.likes && Array.isArray(reply.likes) && reply.likes.some(userId => userId === userData?.user?._id)) ?
                                                    <img onClick={() => { handleReplyLike(reply) }} src={heartfill} alt="liked" className="w-5 h-5 cursor-pointer" /> :
                                                    <GoHeart size={20} onClick={() => { handleReplyLike(reply) }} className={`${mainTextTheme} cursor-pointer transition-all duration-200 ease-in-out ${hoverTheme}`} />}
                                                {allReplies && <p className={`${mainTextTheme} text-xs`}>{reply?.likes?.length}</p>}
                                            </div>
                                        </div>
                                    </div>
                                ))}

                            </div>

                        ))
                    }
                </>}
        </>
    )
}

export default React.memo(PostComments)
