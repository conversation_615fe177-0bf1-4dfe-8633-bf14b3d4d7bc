import mongoose from "mongoose";

const postSchema = new mongoose.Schema({
    author: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    image: {
        type: String,
        required: true,
    },
    type:{
        type: String,
        enum: ['Post', 'Reel'],
        default: 'Post'
    },
    likes: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    comments: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Comment'
    }],
    hideLikes: {
        type: Boolean,
        default: false,
    },
    hideComments: {
        type: Boolean,
        default: false,
    },
    description: {
        type: String,
    },
    saved: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }],
    tagged: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }

}, { timestamps: true });

const Post = mongoose.model('Post', postSchema);

export default Post;

