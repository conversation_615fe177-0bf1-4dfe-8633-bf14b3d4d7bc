import { useNavigate } from "react-router-dom"


const OtpNavbar = ({ page }) => {


    const navigate = useNavigate()


    return (
        <div className={`w-full lg:gap-170 py-[29px] h-[50px] flex ${page === 'login' || page === 'signup' ? 'px-5 md:px-0  justify-between md:justify-around lg:justify-center ' : 'justify-start pl-5 md:pl-100'} items-center dark:bg-black dark:border-b-2 border-b-1 border-[#dbdbdb] dark:border-[#212121]`}>
            <div>
                <h1 onClick={() => { navigate(-1) }} className="heading text-[#262626] dark:text-gray-100 text-3xl tracking-tight font-medium cursor-pointer">Instagram</h1>
            </div>
            {(page === 'login' || page === 'signup') && <div className="flex gap-4 font-semibold text-sm">
                <button onClick={() => navigate('/login')} className=" text-gray-100 bg-[#0095f6] px-4 py-[6px] rounded-lg hover:bg-[#0085ebd4] cursor-pointer active:scale-95 transition-all duration-300 ease-in-out">Log In</button>
                <button onClick={() => navigate('/signup')} className="text-[#0087eb] cursor-pointer active:scale-95 transition-all duration-300 ease-in-out">Sign Up</button>
            </div>}
        </div>
    )
}

export default OtpNavbar
